-- -- Seed a test project with multi-stage budgets based on the ICMS v3 WBS
-- DO $$
-- DECLARE
--     v_user_id uuid := 'e7909c19-9a50-41f1-b1b7-26553bedd0a7';
--     v_client_id uuid;
--     v_wbs_library_id bigint;
--     v_project_id uuid;
--     v_stage1_id bigint;
--     v_stage2_id bigint;
--     v_stage3_id bigint;
--     v_stage4_id bigint;
--     v_snapshot1_id bigint;
--     v_snapshot2_id bigint;
--     v_snapshot3_id bigint;
--     v_snapshot4_id bigint;
--     rec RECORD;
--     num_children integer;
--     child_rate numeric;
-- BEGIN
--     SELECT client_id INTO v_client_id FROM public.client WHERE name = 'Unity';
--     SELECT wbs_library_id INTO v_wbs_library_id FROM public.wbs_library WHERE name = 'ICMS v3';
--     INSERT INTO public.project (name, description, created_by_user_id, client_id, wbs_library_id)
--     VALUES ('ICMS Budget Test Project', 'Seeded project for budget hierarchy testing', v_user_id, v_client_id, v_wbs_library_id)
--     RETURNING project_id INTO v_project_id;
--     INSERT INTO public.project_stage (project_id, name, stage_order, stage)
--     VALUES (v_project_id, 'Stage 1', 1, 1)
--     RETURNING project_stage_id INTO v_stage1_id;
--     INSERT INTO public.project_stage (project_id, name, stage_order, stage)
--     VALUES (v_project_id, 'Stage 2', 2, 2)
--     RETURNING project_stage_id INTO v_stage2_id;
--     INSERT INTO public.project_stage (project_id, name, stage_order, stage)
--     VALUES (v_project_id, 'Stage 3', 3, 3)
--     RETURNING project_stage_id INTO v_stage3_id;
--     INSERT INTO public.project_stage (project_id, name, stage_order, stage)
--     VALUES (v_project_id, 'Stage 4', 4, 4)
--     RETURNING project_stage_id INTO v_stage4_id;
--     INSERT INTO public.budget_snapshot (project_stage_id, freeze_date, freeze_reason, created_by_user_id)
--     VALUES (v_stage1_id, now(), 'Stage 1 budget', v_user_id)
--     RETURNING budget_snapshot_id INTO v_snapshot1_id;
--     FOR rec IN
--         SELECT wbs_library_item_id
--         FROM public.wbs_library_item
--         WHERE wbs_library_id = v_wbs_library_id AND level = 2
--     LOOP
--         INSERT INTO public.budget_snapshot_line_item (
--             budget_snapshot_id,
--             wbs_library_item_id,
--             quantity,
--             material_rate,
--             unit_rate_manual_override,
--             unit_rate
--         )
--         VALUES (
--             v_snapshot1_id,
--             rec.wbs_library_item_id,
--             round((random()*90+10)::numeric,2),
--             round((random()*900+100)::numeric,2),
--             TRUE,
--             round((random()*9000+1000)::numeric,2)
--         );
--     END LOOP;
--     INSERT INTO public.budget_snapshot (project_stage_id, freeze_date, freeze_reason, created_by_user_id)
--     VALUES (v_stage2_id, now(), 'Stage 2 budget', v_user_id)
--     RETURNING budget_snapshot_id INTO v_snapshot2_id;
--     FOR rec IN
--         SELECT l3.wbs_library_item_id, l3.parent_item_id, bl.unit_rate AS parent_rate
--         FROM public.wbs_library_item l3
--         JOIN public.budget_snapshot_line_item bl ON bl.budget_snapshot_id = v_snapshot1_id
--             AND bl.wbs_library_item_id = l3.parent_item_id
--         WHERE l3.wbs_library_id = v_wbs_library_id AND l3.level = 3
--     LOOP
--         SELECT count(*) INTO num_children FROM public.wbs_library_item
--             WHERE parent_item_id = rec.parent_item_id AND level = 3;
--         IF num_children < 1 THEN
--             num_children := 1;
--         END IF;
--         child_rate := round(rec.parent_rate / num_children * (0.75 + random()*0.5), 2);
--         INSERT INTO public.budget_snapshot_line_item (
--             budget_snapshot_id,
--             wbs_library_item_id,
--             quantity,
--             material_rate,
--             unit_rate_manual_override,
--             unit_rate
--         )
--         VALUES (
--             v_snapshot2_id,
--             rec.wbs_library_item_id,
--             1,
--             child_rate,
--             TRUE,
--             child_rate
--         );
--     END LOOP;
--     INSERT INTO public.budget_snapshot (project_stage_id, freeze_date, freeze_reason, created_by_user_id)
--     VALUES (v_stage3_id, now(), 'Stage 3 budget', v_user_id)
--     RETURNING budget_snapshot_id INTO v_snapshot3_id;
--     FOR rec IN
--         SELECT l4.wbs_library_item_id, l4.parent_item_id, bl.unit_rate AS parent_rate
--         FROM public.wbs_library_item l4
--         JOIN public.budget_snapshot_line_item bl ON bl.budget_snapshot_id = v_snapshot2_id
--             AND bl.wbs_library_item_id = l4.parent_item_id
--         WHERE l4.wbs_library_id = v_wbs_library_id AND l4.level = 4
--     LOOP
--         SELECT count(*) INTO num_children FROM public.wbs_library_item
--             WHERE parent_item_id = rec.parent_item_id AND level = 4;
--         IF num_children < 1 THEN
--             num_children := 1;
--         END IF;
--         child_rate := round(rec.parent_rate / num_children * (0.85 + random()*0.3), 2);
--         INSERT INTO public.budget_snapshot_line_item (
--             budget_snapshot_id,
--             wbs_library_item_id,
--             quantity,
--             material_rate,
--             unit_rate_manual_override,
--             unit_rate
--         )
--         VALUES (
--             v_snapshot3_id,
--             rec.wbs_library_item_id,
--             1,
--             child_rate,
--             TRUE,
--             child_rate
--         );
--     END LOOP;
--     INSERT INTO public.budget_snapshot (project_stage_id, freeze_date, freeze_reason, created_by_user_id)
--     VALUES (v_stage4_id, now(), 'Stage 4 budget', v_user_id)
--     RETURNING budget_snapshot_id INTO v_snapshot4_id;
--     FOR rec IN
--         SELECT wbs_library_item_id, unit_rate
--         FROM public.budget_snapshot_line_item
--         WHERE budget_snapshot_id = v_snapshot3_id
--     LOOP
--         child_rate := round(rec.unit_rate * (0.95 + random()*0.1), 2);
--         INSERT INTO public.budget_snapshot_line_item (
--             budget_snapshot_id,
--             wbs_library_item_id,
--             quantity,
--             material_rate,
--             unit_rate_manual_override,
--             unit_rate
--         )
--         VALUES (
--             v_snapshot4_id,
--             rec.wbs_library_item_id,
--             1,
--             child_rate,
--             TRUE,
--             child_rate
--         );
--     END LOOP;
-- END;
-- $$;
