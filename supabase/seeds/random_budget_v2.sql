-- begin;
-- -- seed test project with ICMS wbs library and four stages of budgets
-- do $$
-- declare
--     v_project_id uuid := gen_random_uuid();
--     v_client_id uuid := (select client_id from public.client limit 1);
--     v_user_id uuid := (select user_id from profile limit 1);
--     v_stage1_id bigint;
--     v_stage2_id bigint;
--     v_stage3_id bigint;
--     v_stage4_id bigint;
--     v_snapshot1_id bigint;
--     v_snapshot2_id bigint;
--     v_snapshot3_id bigint;
--     v_snapshot4_id bigint;
--     v_amount numeric;
--     v_children int;
-- begin
--     -- create project
--     insert into public.project (project_id, name, description, created_by_user_id, client_id, wbs_library_id)
--     values (v_project_id, 'ICMS Budget Test Project', 'Seeded project with staged budgets', v_user_id, v_client_id, 1);
--     -- create stages
--     insert into public.project_stage (project_id, name, stage_order)
--     values (v_project_id, 'Stage 1', 1)
--     returning project_stage_id into v_stage1_id;
--     insert into public.project_stage (project_id, name, stage_order)
--     values (v_project_id, 'Stage 2', 2)
--     returning project_stage_id into v_stage2_id;
--     insert into public.project_stage (project_id, name, stage_order)
--     values (v_project_id, 'Stage 3', 3)
--     returning project_stage_id into v_stage3_id;
--     insert into public.project_stage (project_id, name, stage_order)
--     values (v_project_id, 'Stage 4', 4)
--     returning project_stage_id into v_stage4_id;
--     -- create budget snapshots
--     insert into public.budget_snapshot (project_stage_id, created_by_user_id, freeze_reason)
--     values (v_stage1_id, v_user_id, 'Stage 1 budget')
--     returning budget_snapshot_id into v_snapshot1_id;
--     insert into public.budget_snapshot (project_stage_id, created_by_user_id, freeze_reason)
--     values (v_stage2_id, v_user_id, 'Stage 2 budget')
--     returning budget_snapshot_id into v_snapshot2_id;
--     insert into public.budget_snapshot (project_stage_id, created_by_user_id, freeze_reason)
--     values (v_stage3_id, v_user_id, 'Stage 3 budget')
--     returning budget_snapshot_id into v_snapshot3_id;
--     insert into public.budget_snapshot (project_stage_id, created_by_user_id, freeze_reason)
--     values (v_stage4_id, v_user_id, 'Stage 4 budget')
--     returning budget_snapshot_id into v_snapshot4_id;
--     -- temp tables for amounts
--     create temp table tmp_stage1 (wbs_id uuid, amt numeric);
--     create temp table tmp_stage2 (wbs_id uuid, amt numeric);
--     create temp table tmp_stage3 (wbs_id uuid, amt numeric);
--     -- stage 1 budget across level 2 items
--     for rec in
--         select wbs_library_item_id
--         from public.wbs_library_item
--         where wbs_library_id = 1 and level = 2
--     loop
--         v_amount := round((10000 + random() * 90000)::numeric, 2);
--         insert into public.budget_snapshot_line_item
--             (budget_snapshot_id, wbs_library_item_id, quantity, material_rate, unit_rate)
--         values (v_snapshot1_id, rec.wbs_library_item_id, 1, v_amount, v_amount);
--         insert into tmp_stage1 values (rec.wbs_library_item_id, v_amount);
--     end loop;
--     -- stage 2 budget spread to level 3 with +/-25%
--     for rec in select * from tmp_stage1 loop
--         select count(*) into v_children from public.wbs_library_item where parent_item_id = rec.wbs_id;
--         if v_children = 0 then continue; end if;
--         for child in
--             select wbs_library_item_id from public.wbs_library_item where parent_item_id = rec.wbs_id
--         loop
--             v_amount := rec.amt / v_children * (1 + (random() - 0.5) * 0.5);
--             v_amount := round(v_amount, 2);
--             insert into public.budget_snapshot_line_item
--                 (budget_snapshot_id, wbs_library_item_id, quantity, material_rate, unit_rate)
--             values (v_snapshot2_id, child.wbs_library_item_id, 1, v_amount, v_amount);
--             insert into tmp_stage2 values (child.wbs_library_item_id, v_amount);
--         end loop;
--     end loop;
--     -- stage 3 budget spread to level 4 with +/-15%
--     for rec in select * from tmp_stage2 loop
--         select count(*) into v_children from public.wbs_library_item where parent_item_id = rec.wbs_id;
--         if v_children = 0 then continue; end if;
--         for child in
--             select wbs_library_item_id from public.wbs_library_item where parent_item_id = rec.wbs_id
--         loop
--             v_amount := rec.amt / v_children * (1 + (random() - 0.5) * 0.3);
--             v_amount := round(v_amount, 2);
--             insert into public.budget_snapshot_line_item
--                 (budget_snapshot_id, wbs_library_item_id, quantity, material_rate, unit_rate)
--             values (v_snapshot3_id, child.wbs_library_item_id, 1, v_amount, v_amount);
--             insert into tmp_stage3 values (child.wbs_library_item_id, v_amount);
--         end loop;
--     end loop;
--     -- stage 4 final budget with +/-5%
--     for rec in select * from tmp_stage3 loop
--         v_amount := rec.amt * (1 + (random() - 0.5) * 0.1);
--         v_amount := round(v_amount, 2);
--         insert into public.budget_snapshot_line_item
--             (budget_snapshot_id, wbs_library_item_id, quantity, material_rate, unit_rate)
--         values (v_snapshot4_id, rec.wbs_id, 1, v_amount, v_amount);
--         insert into public.budget_line_item_current
--             (project_id, wbs_library_item_id, quantity, material_rate, unit_rate)
--         values (v_project_id, rec.wbs_id, 1, v_amount, v_amount);
--     end loop;
-- end;
-- $$;
-- commit;
