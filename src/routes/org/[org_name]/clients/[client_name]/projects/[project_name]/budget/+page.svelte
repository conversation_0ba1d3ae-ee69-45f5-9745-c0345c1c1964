<script lang="ts">
	import { Button, buttonVariants } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import * as Form from '$lib/components/ui/form';
	import FloppyDiskIcon from 'phosphor-svelte/lib/FloppyDisk';
	import XIcon from 'phosphor-svelte/lib/X';
	import { Plus as PlusIcon } from 'phosphor-svelte';
	import { formatCurrency } from '$lib/utils.js';
	import CheckIcon from 'phosphor-svelte/lib/Check';
	import CaretUpDownIcon from 'phosphor-svelte/lib/CaretUpDown';
	import { tick } from 'svelte';
	import * as Command from '$lib/components/ui/command/index.js';
	import * as Popover from '$lib/components/ui/popover/index.js';
	import { cn } from '$lib/utils.js';
	import { useId } from 'bits-ui';
	import { superForm } from 'sveltekit-superforms';
	import { toast } from 'svelte-sonner';
	import { SvelteSet } from 'svelte/reactivity';
	import BudgetCurrentNode from '$lib/components/budget-current-node.svelte';
	import {
		calculateUnitRate,
		type BudgetLineItem,
		createCurrentBudgetHierarchy,
	} from '$lib/budget_utils.js';
	import { slide } from 'svelte/transition';
	import { Checkbox } from '$lib/components/ui/checkbox/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { useSidebar } from '$lib/components/ui/sidebar';

	const { data } = $props();

	const sidebar = useSidebar();

	const wbsItems = $derived(data.wbsItems);

	// Create the budget hierarchy on the client side using derived state
	const budgetHierarchy = $derived(() => {
		if (!data.allWbsItems || !data.rawCurrentItems) {
			return null;
		}
		return createCurrentBudgetHierarchy(data.allWbsItems, data.rawCurrentItems);
	});

	// Get the current hierarchy value
	const currentHierarchy = $derived(budgetHierarchy());

	// State for hiding zero values
	let hideZeros = $state(true);

	const form = superForm(data.form, {
		id: 'new-budget-item-form',
		onChange: async (event) => {
			if (
				event.paths.length === 1 &&
				[
					'unit_rate',
					'material_rate',
					'labor_rate',
					'productivity_per_hour',
					'unit_rate_manual_override',
				].includes(event.paths[0])
			) {
				$formData.unit_rate = calculateUnitRate($formData);
			}
		},
		onUpdated: async ({ form: f }) => {
			if (f.message) {
				if (f.message?.type === 'error') {
					toast.error(f.message?.text);
				} else if (f.message?.type === 'success') {
					toast.success(f.message?.text);
					cancelEditing();
				}
			}
		},
	});

	const { form: formData, enhance } = form;

	let open = $state(false);
	let triggerRef = $state<HTMLButtonElement>(null!);

	// We want to refocus the trigger button when the user selects
	// an item from the list so users can continue navigating the
	// rest of the form with the keyboard.
	function closeAndFocusTrigger(triggerId: string) {
		open = false;
		tick().then(() => {
			document.getElementById(triggerId)?.focus();
		});
	}
	const triggerId = useId();

	// const wbsOptions = $derived.by(() => {
	// 	return data.wbsItems.map((item) => ({
	// 		label: `${item.code}: ${item.description}`,
	// 		value: String(item.wbs_library_item_id),
	// 	}));
	// });

	function getWbsItemName(wbsId: string | null | undefined): string {
		if (!wbsId) return 'None';

		const item = data.wbsItems.find((item) => item.value === wbsId);
		return item ? item.label : 'Unknown';
	}

	// Props
	let projectId = $derived(data.project.project_id);

	// State
	let isAddingNewItem = $state(!data.rawCurrentItems || data.rawCurrentItems.length === 0);
	let expandedNodeIds = new SvelteSet<string>();

	// Toggle expanded/collapsed state of a node
	function toggleNodeExpanded(nodeId: string) {
		if (expandedNodeIds.has(nodeId)) {
			expandedNodeIds.delete(nodeId);
		} else {
			expandedNodeIds.add(nodeId);
		}
	}

	// Create an empty budget item
	function createEmptyBudgetItem(): BudgetLineItem {
		return {
			project_id: projectId,
			wbs_library_item_id: '',
			quantity: 0,
			unit: '',
			material_rate: 0,
			labor_rate: null,
			productivity_per_hour: null,
			unit_rate_manual_override: false,
			unit_rate: 0,
			remarks: null,
			cost_certainty: null,
			design_certainty: null,
		};
	}

	// Cancel editing
	function cancelEditing() {
		// Reset the form data
		$formData = createEmptyBudgetItem();
		isAddingNewItem = false;
	}

	const isLoading = $derived(false);
</script>

<div class="overflow-hidden p-4">
	<div class="mb-4 flex items-center justify-between">
		<div class="w-full">
			<h1 class="text-2xl font-bold">Project Budget</h1>
			<div class="flex w-full items-center justify-between">
				<div class="flex items-start gap-3">
					<div class="grid gap-2">
						<Label for="hide-zeros">Hide zero values</Label>
					</div>
					<Checkbox id="hide-zeros" bind:checked={hideZeros} />
				</div>
				<Button
					onclick={() => (isAddingNewItem = !isAddingNewItem)}
					class="flex items-center gap-2"
				>
					<PlusIcon class="size-4" />
					<span>Add Budget Item</span>
				</Button>
			</div>
		</div>
	</div>

	{#if isLoading}
		<div class="flex justify-center p-8">
			<p>Loading budget data...</p>
		</div>
	{:else}
		<div
			class="-mr-4 -ml-4 overflow-x-scroll tabular-nums"
			style={`max-width: ${
				sidebar.open
					? 'calc(100vw - var(--sidebar-width) - 1rem)'
					: 'calc(100vw - var(--sidebar-width-icon) - 1rem)'
			}; max-height: calc(100vh - 14rem); position: relative;`}
		>
			<table class="budget-table relative table-fixed border-collapse">
				<thead class="sticky top-0">
					<tr class="bg-muted h-10 border-t text-sm font-medium">
						<th class="w-24 py-2 pr-2 pl-8 text-left">WBS Code</th>
						<th class="w-48 px-2 text-left">Description</th>
						<th class="w-20 px-2 text-right">Quantity</th>
						<th class="w-16 px-2 text-left">Unit</th>
						<th class="w-24 border-b px-2 text-center" colspan="3">Rate Calculation</th>
						<th class="w-24 px-2 text-right">Unit Rate</th>
						<th class="w-16 px-2 text-right">Factor</th>
						<th class="w-24 px-2 text-right">Subtotal</th>
						<th class="w-24 px-2 text-right">Cost Certainty</th>
						<th class="w-24 px-2 text-right">Design Certainty</th>
					</tr>
					<tr class="bg-muted h-10 text-sm font-medium">
						<th colspan="4"></th>
						<th class="w-24 px-2 text-right">Material Rate</th>
						<th class="w-24 px-2 text-right">Labor Rate</th>
						<th class="w-24 px-2 text-right">Productivity</th>
						<th colspan="5"></th>
					</tr>
				</thead>
				<tbody>
					{#if isAddingNewItem}
						<tr class="bg-muted/50 border-b" in:slide={{ duration: 300 }}>
							<td colspan="2" class="p-1">
								<Form.Field {form} name="wbs_library_item_id" class="flex flex-col">
									<Popover.Root bind:open>
										<Form.Control id={triggerId}>
											{#snippet children({ props })}
												<Form.Label class="sr-only">WBS Code</Form.Label>
												<Popover.Trigger
													class={cn(
														buttonVariants({ variant: 'outline' }),
														'w-full justify-between',
														!$formData.wbs_library_item_id && 'text-muted-foreground',
													)}
													role="combobox"
													bind:ref={triggerRef}
													{...props}
												>
													{$formData.wbs_library_item_id
														? getWbsItemName($formData.wbs_library_item_id)
														: 'Select WBS code'}
													<CaretUpDownIcon class="ml-2 size-4 shrink-0 opacity-50" />
												</Popover.Trigger>
												<input hidden value={$formData.wbs_library_item_id} name={props.name} />
											{/snippet}
										</Form.Control>
										<Popover.Content
											class="w-(--bits-popover-anchor-width) p-0"
											side="bottom"
											align="start"
										>
											<Command.Root>
												<Command.Input autofocus placeholder="Search parent items..." class="h-9" />
												<Command.Empty>No matching items found.</Command.Empty>
												<Command.Group class="max-h-[300px] overflow-y-auto">
													{#each wbsItems as option (option.value)}
														<Command.Item
															value={option.label}
															onSelect={() => {
																$formData.wbs_library_item_id = option.value;
																closeAndFocusTrigger(triggerId);
															}}
														>
															{option.label}
															<CheckIcon
																class={cn(
																	'ml-auto size-4',
																	option.value !== $formData.wbs_library_item_id &&
																		'text-transparent',
																)}
															/>
														</Command.Item>
													{/each}
												</Command.Group>
											</Command.Root>
										</Popover.Content>
									</Popover.Root>
									<Form.FieldErrors />
								</Form.Field>
							</td>
							<td class="p-1">
								<Form.Field {form} name="quantity">
									<Form.Control>
										{#snippet children({ props })}
											<Form.Label class="sr-only">Quantity</Form.Label>
											<Input
												{...props}
												type="number"
												step="0.01"
												bind:value={$formData.quantity}
												placeholder="Quantity"
												class="w-full"
											/>
										{/snippet}
									</Form.Control>
									<Form.FieldErrors />
								</Form.Field>
							</td>
							<td class="p-1">
								<Form.Field {form} name="unit">
									<Form.Control>
										{#snippet children({ props })}
											<Form.Label class="sr-only">Unit</Form.Label>
											<Input
												{...props}
												type="text"
												bind:value={$formData.unit}
												placeholder="m^2"
												class="w-full"
											/>
										{/snippet}
									</Form.Control>
									<Form.FieldErrors />
								</Form.Field>
							</td>
							<td class="p-1">
								<Form.Field {form} name="material_rate">
									<Form.Control>
										{#snippet children({ props })}
											<Form.Label class="sr-only">Material Rate</Form.Label>
											<Input
												{...props}
												type="number"
												placeholder="Material Rate"
												step="0.01"
												bind:value={$formData.material_rate}
												disabled={$formData.unit_rate_manual_override}
												class="w-full"
											/>
										{/snippet}
									</Form.Control>
									<Form.FieldErrors />
								</Form.Field>
							</td>
							<td class="p-1">
								<Form.Field {form} name="labor_rate">
									<Form.Control>
										{#snippet children({ props })}
											<Form.Label class="sr-only">Labor Rate</Form.Label>
											<Input
												{...props}
												type="number"
												step="0.01"
												placeholder="0"
												bind:value={$formData.labor_rate}
												disabled={$formData.unit_rate_manual_override}
												class="w-full"
											/>
										{/snippet}
									</Form.Control>
									<Form.FieldErrors />
								</Form.Field>
							</td>
							<td class="p-1">
								<Form.Field {form} name="productivity_per_hour">
									<Form.Control>
										{#snippet children({ props })}
											<Form.Label class="sr-only">Productivity (qty/hour)</Form.Label>
											<Input
												{...props}
												type="number"
												step="0.01"
												bind:value={$formData.productivity_per_hour}
												disabled={$formData.unit_rate_manual_override}
												placeholder="0"
												class="w-full"
											/>
										{/snippet}
									</Form.Control>
									<Form.FieldErrors />
								</Form.Field>
							</td>
							<td class="relative p-1">
								<div class="flex items-center gap-2">
									<Form.Field {form} name="unit_rate">
										<Form.Control>
											{#snippet children({ props })}
												<Form.Label class="sr-only">
													{$formData.unit_rate_manual_override
														? 'Manual Unit Rate'
														: 'Calculated Unit Rate'}
												</Form.Label>
												<Input
													{...props}
													type="number"
													step="0.01"
													bind:value={$formData.unit_rate}
													disabled={!$formData.unit_rate_manual_override}
													placeholder="0"
													class="w-full"
												/>
											{/snippet}
										</Form.Control>
										<Form.FieldErrors />
									</Form.Field>
								</div>

								<Form.Field
									{form}
									name="unit_rate_manual_override"
									class="absolute -right-1 -bottom-8 z-10 flex min-w-max items-center gap-2"
								>
									<Form.Control>
										{#snippet children({ props })}
											<Form.Label>Manual Unit Rate</Form.Label>
											<Checkbox {...props} bind:checked={$formData.unit_rate_manual_override} />
										{/snippet}
									</Form.Control>
								</Form.Field>
							</td>

							<td class="p-1">
								<Form.Field {form} name="factor">
									<Form.Control>
										{#snippet children({ props })}
											<Form.Label class="sr-only">Factor</Form.Label>
											<Input
												{...props}
												type="number"
												step="0.01"
												bind:value={$formData.factor}
												placeholder="1"
												class="w-full"
											/>
										{/snippet}
									</Form.Control>
									<Form.FieldErrors />
								</Form.Field>
							</td>
							<td class="p-1 text-right">
								{formatCurrency(
									$formData.quantity * ($formData.unit_rate || 0) * ($formData.factor || 1),
								)}
							</td>
							<td class="p-1">
								<Form.Field {form} name="cost_certainty">
									<Form.Control>
										{#snippet children({ props })}
											<Form.Label class="sr-only">Cost Certainty</Form.Label>
											<Input
												{...props}
												type="number"
												step="0.01"
												placeholder="%"
												bind:value={$formData.cost_certainty}
												class="w-full"
											/>
										{/snippet}
									</Form.Control>
									<Form.FieldErrors />
								</Form.Field>
							</td>
							<td class="p-1">
								<Form.Field {form} name="design_certainty">
									<Form.Control>
										{#snippet children({ props })}
											<Form.Label class="sr-only">Design Certainty</Form.Label>
											<Input
												{...props}
												type="number"
												step="0.01"
												placeholder="%"
												bind:value={$formData.design_certainty}
												class="w-full"
											/>
										{/snippet}
									</Form.Control>
									<Form.FieldErrors />
								</Form.Field>
							</td>
						</tr>
						<tr class="border-b">
							<td colspan="10"></td>
							<td class="p-1">
								<Button variant="outline" class="w-full" onclick={cancelEditing}>
									<XIcon class="mr-1 size-4" />
									Cancel
								</Button>
							</td>
							<td class="p-1">
								<form action="?/upsertBudgetItem" method="post" use:enhance>
									<input type="hidden" name="project_id" value={projectId} />
									<input
										type="hidden"
										name="budget_line_item_id"
										value={$formData.budget_line_item_id || ''}
									/>
									<input
										type="hidden"
										name="wbs_library_item_id"
										value={$formData.wbs_library_item_id}
									/>
									<input type="hidden" name="quantity" value={$formData.quantity} />
									<input type="hidden" name="unit" value={$formData.unit} />
									<input type="hidden" name="material_rate" value={$formData.material_rate} />
									<input type="hidden" name="labor_rate" value={$formData.labor_rate} />
									<input
										type="hidden"
										name="productivity_per_hour"
										value={$formData.productivity_per_hour}
									/>
									<input type="hidden" name="unit_rate" value={$formData.unit_rate} />
									<input
										type="hidden"
										name="unit_rate_manual_override"
										value={$formData.unit_rate_manual_override}
									/>
									<input type="hidden" name="factor" value={$formData.factor} />
									<input type="hidden" name="cost_certainty" value={$formData.cost_certainty} />
									<input type="hidden" name="design_certainty" value={$formData.design_certainty} />
									<Button class="w-full" type="submit">
										<FloppyDiskIcon class="size-4" />
										Save
									</Button>
								</form>
							</td>
						</tr>
					{/if}
					{#if currentHierarchy}
						<BudgetCurrentNode
							node={currentHierarchy}
							indent={0}
							expanded={expandedNodeIds}
							toggle={toggleNodeExpanded}
							data={data.form}
							{projectId}
							{hideZeros}
						/>
					{/if}
				</tbody>
			</table>
		</div>
	{/if}
</div>

<style>
	.budget-table {
		width: max-content;
		min-width: 100%;
	}

	.overflow-x-scroll {
		/* overscroll-behavior-x: contain; */
		padding-block: 0.5rem;
	}
</style>
