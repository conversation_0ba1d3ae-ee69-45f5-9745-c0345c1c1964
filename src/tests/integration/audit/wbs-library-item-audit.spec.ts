import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { createClient } from '@supabase/supabase-js';
import type { Database } from '$lib/database.types';
import { SUPABASE_SERVICE_KEY } from '$env/static/private';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';

describe('WBS Library Item Audit Logging', () => {
	let supabase: ReturnType<typeof createClient<Database>>;
	let testWbsLibraryId: number;
	let testClientId: string;
	let testWbsItemId: string;

	beforeEach(async () => {
		supabase = createClient<Database>(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_KEY);

		// Create test client with required created_by_user_id
		const { data: client, error: clientError } = await supabase
			.from('client')
			.insert({
				name: 'Test Client for WBS Audit',
				org_id: '6d8f7927-c31c-46b0-9dfd-fbc2b530088a', // Use existing org from seeds
				created_by_user_id: 'e7909c19-9a50-41f1-b1b7-26553bedd0a7', // Test user from seeds
			})
			.select('client_id')
			.single();

		if (clientError) throw clientError;
		testClientId = client.client_id;

		// Get the custom WBS library (ID = 1 from seeds)
		testWbsLibraryId = 1;
	});

	afterEach(async () => {
		// Clean up test data
		if (testWbsItemId) {
			await supabase.from('wbs_library_item').delete().eq('wbs_library_item_id', testWbsItemId);
		}
		if (testClientId) {
			await supabase.from('client').delete().eq('client_id', testClientId);
		}
	});

	it('should create audit record on INSERT', async () => {
		// Insert a new WBS library item
		const { data: wbsItem, error: insertError } = await supabase
			.from('wbs_library_item')
			.insert({
				wbs_library_id: testWbsLibraryId,
				level: 1,
				in_level_code: '01',
				code: '01',
				description: 'Test WBS Item for Audit',
				cost_scope: 'Test cost scope',
				item_type: 'Custom',
				client_id: testClientId,
			})
			.select('wbs_library_item_id')
			.single();

		if (insertError) throw insertError;
		testWbsItemId = wbsItem.wbs_library_item_id;

		// Check that audit record was created
		const { data: auditRecords, error: auditError } = await supabase
			.from('wbs_library_item_audit')
			.select('*')
			.eq('wbs_library_item_id', testWbsItemId)
			.eq('operation_type', 'INSERT');

		if (auditError) throw auditError;

		expect(auditRecords).toHaveLength(1);
		const auditRecord = auditRecords[0];

		expect(auditRecord.operation_type).toBe('INSERT');
		expect(auditRecord.wbs_library_item_id).toBe(testWbsItemId);
		expect(auditRecord.description).toBe('Test WBS Item for Audit');
		expect(auditRecord.item_type).toBe('Custom');
		expect(auditRecord.client_id).toBe(testClientId);
		expect(auditRecord.new_values).toBeDefined();
		expect(auditRecord.old_values).toBeNull();
		expect(auditRecord.changed_by).toBe('00000000-0000-0000-0000-000000000000'); // System user
		expect(auditRecord.changed_at).toBeDefined();
	});

	it('should create audit record on UPDATE', async () => {
		// First insert a WBS library item
		const { data: wbsItem, error: insertError } = await supabase
			.from('wbs_library_item')
			.insert({
				wbs_library_id: testWbsLibraryId,
				level: 1,
				in_level_code: '02',
				code: '02',
				description: 'Original Description',
				cost_scope: 'Original cost scope',
				item_type: 'Custom',
				client_id: testClientId,
			})
			.select('wbs_library_item_id')
			.single();

		if (insertError) throw insertError;
		testWbsItemId = wbsItem.wbs_library_item_id;

		// Update the WBS library item
		const { error: updateError } = await supabase
			.from('wbs_library_item')
			.update({
				description: 'Updated Description',
				cost_scope: 'Updated cost scope',
			})
			.eq('wbs_library_item_id', testWbsItemId);

		if (updateError) throw updateError;

		// Check that audit record was created for UPDATE
		const { data: auditRecords, error: auditError } = await supabase
			.from('wbs_library_item_audit')
			.select('*')
			.eq('wbs_library_item_id', testWbsItemId)
			.eq('operation_type', 'UPDATE');

		if (auditError) throw auditError;

		expect(auditRecords).toHaveLength(1);
		const auditRecord = auditRecords[0];

		expect(auditRecord.operation_type).toBe('UPDATE');
		expect(auditRecord.wbs_library_item_id).toBe(testWbsItemId);
		expect(auditRecord.description).toBe('Updated Description');
		expect(auditRecord.old_values).toBeDefined();
		expect(auditRecord.new_values).toBeDefined();
		expect(auditRecord.changed_by).toBe('00000000-0000-0000-0000-000000000000'); // System user
		expect(auditRecord.changed_at).toBeDefined();

		// Verify old and new values contain the expected data
		const oldValues = auditRecord.old_values as { description: string; cost_scope: string };
		const newValues = auditRecord.new_values as { description: string; cost_scope: string };

		expect(oldValues.description).toBe('Original Description');
		expect(oldValues.cost_scope).toBe('Original cost scope');
		expect(newValues.description).toBe('Updated Description');
		expect(newValues.cost_scope).toBe('Updated cost scope');
	});

	it('should create audit record on DELETE', async () => {
		// First insert a WBS library item
		const { data: wbsItem, error: insertError } = await supabase
			.from('wbs_library_item')
			.insert({
				wbs_library_id: testWbsLibraryId,
				level: 1,
				in_level_code: '03',
				code: '03',
				description: 'Item to Delete',
				cost_scope: 'Delete test cost scope',
				item_type: 'Custom',
				client_id: testClientId,
			})
			.select('wbs_library_item_id')
			.single();

		if (insertError) throw insertError;
		testWbsItemId = wbsItem.wbs_library_item_id;

		// Delete the WBS library item
		const { error: deleteError } = await supabase
			.from('wbs_library_item')
			.delete()
			.eq('wbs_library_item_id', testWbsItemId);

		if (deleteError) throw deleteError;

		// Check that audit record was created for DELETE
		const { data: auditRecords, error: auditError } = await supabase
			.from('wbs_library_item_audit')
			.select('*')
			.eq('wbs_library_item_id', testWbsItemId)
			.eq('operation_type', 'DELETE');

		if (auditError) throw auditError;

		expect(auditRecords).toHaveLength(1);
		const auditRecord = auditRecords[0];

		expect(auditRecord.operation_type).toBe('DELETE');
		expect(auditRecord.wbs_library_item_id).toBe(testWbsItemId);
		expect(auditRecord.description).toBe('Item to Delete');
		expect(auditRecord.old_values).toBeDefined();
		expect(auditRecord.new_values).toBeNull();
		expect(auditRecord.changed_by).toBe('00000000-0000-0000-0000-000000000000'); // System user
		expect(auditRecord.changed_at).toBeDefined();

		// Clear testWbsItemId since item is deleted
		testWbsItemId = '';
	});

	it('should respect RLS policies for audit records', async () => {
		// This test verifies that audit records are accessible via the service role
		// In a real scenario, you'd test with different user contexts and RLS policies

		// Verify we can query audit records with service role
		const { data: auditRecords, error } = await supabase
			.from('wbs_library_item_audit')
			.select('audit_id')
			.limit(1);

		// Should be able to access audit records with service role
		expect(error).toBeNull();
		expect(auditRecords).toBeDefined();
	});
});
