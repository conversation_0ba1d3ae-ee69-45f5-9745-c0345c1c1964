<script lang="ts">
	import BudgetCurrentNode from '$lib/components/budget-current-node.svelte';
	import NotePencilIcon from 'phosphor-svelte/lib/NotePencil';
	import FloppyDiskIcon from 'phosphor-svelte/lib/FloppyDisk';
	import XIcon from 'phosphor-svelte/lib/X';
	import CaretDownIcon from 'phosphor-svelte/lib/CaretDown';
	import CaretRightIcon from 'phosphor-svelte/lib/CaretRight';
	import { formatCurrency, formatPercentage } from '$lib/utils';
	import { SvelteSet } from 'svelte/reactivity';
	import {
		calculateUnitRate,
		type BudgetLineItem,
		type CurrentBudgetNode,
	} from '$lib/budget_utils';
	import Button from '$lib/components/ui/button/button.svelte';
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { superForm, type Infer, type SuperValidated } from 'sveltekit-superforms';
	import { budgetItemSchema } from '$lib/schemas/project';
	import { toast } from 'svelte-sonner';

	let {
		node,
		indent,
		expanded,
		toggle,
		data,
		projectId,
		hideZeros,
	}: {
		node: CurrentBudgetNode;
		indent: number;
		expanded: SvelteSet<string>;
		toggle: (nodeId: string) => void;
		data: SuperValidated<Infer<typeof budgetItemSchema>>;
		projectId: string;
		hideZeros: boolean;
	} = $props();

	// true if this node is in the expanded set
	const isOpen = $derived(node.id && !expanded.has(node.id));
	const isEditing = new SvelteSet<BudgetLineItem['budget_line_item_id']>();

	// Called when the caret is clicked:
	function onToggle() {
		if (node.id) toggle(node.id);
	}

	const form = superForm(data, {
		id: `budget-item-${node.id}-${node.data.budgetData?.budget_line_item_id ?? '0'}`,
		warnings: {
			duplicateId: false,
		},
		onChange: async (event) => {
			if (
				event.paths.length === 1 &&
				[
					'unit_rate',
					'material_rate',
					'labor_rate',
					'productivity_per_hour',
					'unit_rate_manual_override',
				].includes(event.paths[0])
			) {
				$formData.unit_rate = calculateUnitRate($formData);
			}
		},
		onUpdated: async ({ form: f }) => {
			if (f.message) {
				if (f.message?.type === 'error') {
					toast.error(f.message?.text);
				} else if (f.message?.type === 'success') {
					toast.success(f.message?.text);
					cancelEditing(f.data.budget_line_item_id);
				}
			}
		},
	});

	const { form: formData, enhance } = form;

	function createEmptyBudgetItem(): BudgetLineItem {
		return {
			project_id: projectId,
			wbs_library_item_id: '',
			quantity: 0,
			unit: '',
			material_rate: 0,
			labor_rate: null,
			productivity_per_hour: null,
			unit_rate_manual_override: false,
			unit_rate: 0,
			remarks: null,
			cost_certainty: null,
			design_certainty: null,
		};
	}

	// Start editing a budget item
	function startEditing(item: BudgetLineItem) {
		$formData = { ...item };
		isEditing.add(item.budget_line_item_id);
	}

	// Cancel editing
	function cancelEditing(id: BudgetLineItem['budget_line_item_id']) {
		// Reset the form data
		$formData = createEmptyBudgetItem();
		isEditing.delete(id);
	}
</script>

{#if !hideZeros || node.value || node.data.subtotal > 0}
	<!-- Show total cost for nodes with children or no budget data -->
	{#if (node.value !== node.data.subtotal && (node.children?.length || 0) > 0) || !node.data.budgetData}
		<tr
			class="group hover:bg-muted/20 border-b {(node.children?.length || 0) > 0
				? 'font-medium'
				: ''}"
		>
			<td class="py-3 pr-2 pl-8">
				<div class="flex items-center">
					{#if node.children?.length}
						<button onclick={onToggle} aria-label={isOpen ? 'Collapse' : 'Expand'}>
							{#if isOpen}
								<CaretDownIcon class="size-4" />
							{:else}
								<CaretRightIcon class="size-4" />
							{/if}
						</button>
						<span class="ml-2">{node.data.code}</span>
					{:else}
						<span class="ml-6">{node.data.code}</span>
					{/if}
				</div>
			</td>
			<td class="p-1" style="padding-left: {0.5 + indent * 0.75}rem">
				{node.data.description}
			</td>
			<td class="p-1 text-right"></td>
			<td class="p-1"></td>
			<td class="p-1 text-right"></td>
			<td class="p-1 text-right"></td>
			<td class="p-1 text-right"></td>
			<td class="p-1 text-right"></td>
			<td class="p-1 text-right">
				{node.data.budgetData?.factor && node.data.budgetData.factor !== 1
					? node.data.budgetData.factor
					: ''}
			</td>
			<td class="p-1 text-right">
				{formatCurrency(node.value)}
			</td>
			<td class="p-1 text-right"></td>
			<td class="p-1 text-right"></td>
		</tr>
	{/if}

	<!-- Display budget data if it exists -->
	{#if node.data.budgetData}
		{@const budgetData = node.data.budgetData}
		{#if isEditing.has(budgetData.budget_line_item_id)}
			<!-- Editing form row -->
			<tr class="group hover:bg-muted/20 border-b">
				<td class="py-3 pr-2 pl-8">
					<div class="flex items-center">
						{#if node.children?.length && !hideZeros}
							<button onclick={onToggle} aria-label={isOpen ? 'Collapse' : 'Expand'}>
								{#if isOpen}
									<CaretDownIcon class="size-4" />
								{:else}
									<CaretRightIcon class="size-4" />
								{/if}
							</button>
							<span class="ml-2">{node.data.code}</span>
						{:else}
							<span class="ml-6">{node.data.code}</span>
						{/if}
					</div>
				</td>
				<td class="p-1" style="padding-left: {0.5 + indent * 0.75}rem">
					{node.data.description}
				</td>
				<td class="p-1">
					<Form.Field {form} name="quantity">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label class="sr-only">Quantity</Form.Label>
								<Input
									{...props}
									type="number"
									step="0.01"
									bind:value={$formData.quantity}
									placeholder="Quantity"
									class="w-full"
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</td>
				<td class="p-1">
					<Form.Field {form} name="unit">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label class="sr-only">Unit</Form.Label>
								<Input
									{...props}
									type="text"
									bind:value={$formData.unit}
									placeholder="Unit"
									class="w-full"
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</td>
				<td class="p-1">
					<Form.Field {form} name="material_rate">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label class="sr-only">Material Rate</Form.Label>
								<Input
									{...props}
									type="number"
									placeholder="Material Rate"
									step="0.01"
									bind:value={$formData.material_rate}
									disabled={$formData.unit_rate_manual_override}
									class="w-full"
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</td>
				<td class="p-1">
					<Form.Field {form} name="labor_rate">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label class="sr-only">Labor Rate</Form.Label>
								<Input
									{...props}
									type="number"
									step="0.01"
									placeholder="0"
									bind:value={$formData.labor_rate}
									disabled={$formData.unit_rate_manual_override}
									class="w-full"
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</td>
				<td class="p-1">
					<Form.Field {form} name="productivity_per_hour">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label class="sr-only">Productivity (qty/hour)</Form.Label>
								<Input
									{...props}
									type="number"
									step="0.01"
									bind:value={$formData.productivity_per_hour}
									disabled={$formData.unit_rate_manual_override}
									placeholder="0"
									class="w-full"
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</td>
				<td class="p-1">
					<div class="relative">
						<Form.Field {form} name="unit_rate">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label class="sr-only">
										{$formData.unit_rate_manual_override
											? 'Manual Unit Rate'
											: 'Calculated Unit Rate'}
									</Form.Label>
									<Input
										{...props}
										type="number"
										step="0.01"
										bind:value={$formData.unit_rate}
										disabled={!$formData.unit_rate_manual_override}
										placeholder="Unit Rate"
										class="w-full"
									/>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
						<Form.Field
							{form}
							name="unit_rate_manual_override"
							class="absolute -right-1 -bottom-8 z-10 flex min-w-max items-center gap-2"
						>
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Manual Unit Rate</Form.Label>
									<Checkbox {...props} bind:checked={$formData.unit_rate_manual_override} />
								{/snippet}
							</Form.Control>
						</Form.Field>
					</div>
				</td>
				<td class="p-1">
					<Form.Field {form} name="factor">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label class="sr-only">Factor</Form.Label>
								<Input
									{...props}
									type="number"
									step="0.01"
									bind:value={$formData.factor}
									placeholder="1"
									class="w-full"
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</td>
				<td class="p-1 text-right">
					{formatCurrency(
						$formData.quantity * ($formData.unit_rate || 0) * ($formData.factor || 1),
					)}
				</td>
				<td class="p-1">
					<Form.Field {form} name="cost_certainty">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label class="sr-only">Cost Certainty</Form.Label>
								<Input
									{...props}
									type="number"
									step="0.01"
									placeholder="%"
									bind:value={$formData.cost_certainty}
									class="w-full"
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</td>
				<td class="p-1">
					<Form.Field {form} name="design_certainty">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label class="sr-only">Design Certainty</Form.Label>
								<Input
									{...props}
									type="number"
									step="0.01"
									placeholder="%"
									bind:value={$formData.design_certainty}
									class="w-full"
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</td>
			</tr>
			<!-- Action buttons row -->
			<tr class="border-b">
				<td colspan="10"></td>
				<td class="p-1">
					<Button
						variant="outline"
						class="w-full"
						onclick={() => cancelEditing(budgetData.budget_line_item_id)}
					>
						<XIcon class="mr-1 size-4" />
						Cancel
					</Button>
				</td>
				<td class="p-1">
					<form action="?/upsertBudgetItem" method="post" use:enhance>
						<input type="hidden" name="project_id" value={projectId} />
						<input
							type="hidden"
							name="budget_line_item_id"
							value={budgetData.budget_line_item_id}
						/>
						<input type="hidden" name="wbs_library_item_id" value={node.data.wbs_library_item_id} />
						<input type="hidden" name="quantity" value={$formData.quantity} />
						<input type="hidden" name="unit" value={$formData.unit} />
						<input type="hidden" name="material_rate" value={$formData.material_rate} />
						<input type="hidden" name="labor_rate" value={$formData.labor_rate} />
						<input
							type="hidden"
							name="productivity_per_hour"
							value={$formData.productivity_per_hour}
						/>
						<input type="hidden" name="unit_rate" value={$formData.unit_rate} />
						<input
							type="hidden"
							name="unit_rate_manual_override"
							value={$formData.unit_rate_manual_override}
						/>
						<input type="hidden" name="factor" value={$formData.factor} />
						<input type="hidden" name="cost_certainty" value={$formData.cost_certainty} />
						<input type="hidden" name="design_certainty" value={$formData.design_certainty} />
						<Button class="w-full" type="submit">
							<FloppyDiskIcon class="size-4" />
							Save
						</Button>
					</form>
				</td>
			</tr>
		{:else if budgetData.quantity || budgetData.unit || budgetData.unit_rate}
			<!-- Display row -->
			<tr class="group hover:bg-muted/20 relative border-b">
				<td class="py-3 pr-2 pl-8">
					<div class="flex items-center">
						{#if node.children?.length && !hideZeros}
							<button onclick={onToggle} aria-label={isOpen ? 'Collapse' : 'Expand'}>
								{#if isOpen}
									<CaretDownIcon class="size-4" />
								{:else}
									<CaretRightIcon class="size-4" />
								{/if}
							</button>
							<span class="ml-2">{node.data.code}</span>
						{:else}
							<span class="ml-6">{node.data.code}</span>
						{/if}
					</div>
				</td>
				<td class="p-1" style="padding-left: {0.5 + indent * 0.75}rem">
					{node.data.description}
				</td>
				<td class="p-1 text-right">
					{budgetData.quantity}
				</td>
				<td class="p-1">
					{budgetData.unit ?? ''}
				</td>
				<td class="p-1 text-right">
					{#if !budgetData.unit_rate_manual_override}
						{formatCurrency(budgetData.material_rate)}
					{/if}
				</td>
				<td class="p-1 text-right">
					{#if !budgetData.unit_rate_manual_override}
						{formatCurrency(budgetData.labor_rate ?? 0)}
					{/if}
				</td>
				<td class="p-1 text-right">
					{#if !budgetData.unit_rate_manual_override}
						{formatCurrency(budgetData.productivity_per_hour ?? 0)}
					{/if}
				</td>
				<td class="p-1 text-right">
					{formatCurrency(budgetData.unit_rate)}
				</td>
				<td class="p-1 text-right">
					{budgetData.factor ?? ''}
				</td>
				<td class="p-1 text-right">
					{formatCurrency(node.data.subtotal)}
				</td>
				<td class="p-1 text-center">
					{budgetData.cost_certainty != null ? formatPercentage(budgetData.cost_certainty) : ''}
				</td>
				<td class="p-1 text-center">
					{budgetData.design_certainty != null ? formatPercentage(budgetData.design_certainty) : ''}
				</td>
				<td class="absolute top-1 right-0 z-10 opacity-0 group-hover:opacity-100">
					<Button variant="ghost" size="icon" onclick={() => startEditing(budgetData)}>
						<NotePencilIcon class="size-3" />
					</Button>
				</td>
			</tr>
		{/if}
	{/if}

	<!-- Only render children when this node is open -->
	{#if isOpen && node.children}
		{#each node.children as child (child.id)}
			<BudgetCurrentNode
				node={child}
				indent={indent + 1}
				{expanded}
				{toggle}
				{data}
				{projectId}
				{hideZeros}
			/>
		{/each}
	{/if}
{/if}
